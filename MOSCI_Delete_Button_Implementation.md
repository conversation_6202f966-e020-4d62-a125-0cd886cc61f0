# MOSCI Delete Button Implementation

## Summary
Implemented individual delete button functionality for MOSCI grid rows with validation based on the `Mant` field value.

## Changes Made

### 1. Backend Changes

#### MosciRepository.cs
- **Location:** Lines 38-74
- **Change:** Added `Mant` field retrieval from the database in `GetMosciInfoByOaksId` method
- **Details:** Added try-catch block to safely retrieve the MANT field from the stored procedure result

#### MosciController.cs
- **Location:** Lines 357-410
- **Change:** Added new `CheckDeleteEligibility` method
- **Details:** 
  - Validates if offender ID exists and has `Mant = 'CHG'`
  - Returns JSON response indicating whether delete button should be enabled
  - Handles error cases gracefully

### 2. Frontend Changes

#### View Structure Updates
- **Location:** Lines 111, 180-185, 223-228
- **Changes:**
  - Updated table header column width for delete column
  - Replaced delete checkboxes with individual delete buttons per row
  - Updated template row to include delete button instead of checkbox

#### JavaScript Functionality

##### New Functions Added:
1. **checkDeleteEligibility()** (Lines 790-842)
   - Makes AJAX call to validate delete eligibility
   - Updates button state (enabled/disabled) and styling (red/default)
   - Called after offender ID validation

2. **Individual Delete Button Handler** (Lines 2235-2267)
   - Handles click events for individual row delete buttons
   - Validates required data before deletion
   - Shows confirmation modal

3. **performIndividualDeletion()** (Lines 2269-2310)
   - Performs AJAX deletion for individual rows
   - Clears row data on successful deletion
   - Resets delete button state

##### Integration Points:
- **Auto-populate Integration:** Delete eligibility check integrated into existing offender ID validation flow
- **Row Management:** Delete button initialization added to new row creation and template cloning
- **Modal Integration:** Existing delete confirmation modal updated to handle both individual and bulk deletions

### 3. UI/UX Changes
- **Delete Button States:**
  - Disabled (default gray): When no offender ID or Mant ≠ 'CHG'
  - Enabled (red): When offender ID exists and Mant = 'CHG'
- **Bulk Delete Button:** Hidden but kept for backward compatibility
- **Confirmation Modal:** Reused existing modal for individual deletions

## Functionality Flow

1. **User enters Offender ID** → Auto-populate triggers
2. **Auto-populate completes** → `checkDeleteEligibility()` called
3. **AJAX validation** → Checks if Mant = 'CHG' in database
4. **Button state updated** → Enabled (red) if eligible, disabled (gray) if not
5. **User clicks delete** → Confirmation modal shown
6. **User confirms** → Individual deletion performed
7. **Success** → Row cleared, button disabled

## Validation Rules

- **Enable Delete Button When:**
  - Offender ID exists in MOSCI records
  - Mant field equals 'CHG' (case-insensitive)
  - All required data present (prefix, offender ID, schedule date)

- **Disable Delete Button When:**
  - No offender ID entered
  - Offender ID doesn't exist in MOSCI records
  - Mant field is not 'CHG'
  - Missing required data

## Error Handling

- **Network Errors:** Button disabled, error logged to console
- **Missing Data:** Alert shown, operation cancelled
- **Database Errors:** Graceful error messages returned to user
- **Validation Failures:** Clear feedback provided

## Testing Scenarios

1. **Empty Row:** Delete button should be disabled (gray)
2. **Valid Offender with Mant='CHG':** Delete button enabled (red)
3. **Valid Offender with Mant≠'CHG':** Delete button disabled (gray)
4. **Invalid Offender ID:** Delete button disabled (gray)
5. **Network Error:** Delete button disabled (gray)
6. **Successful Deletion:** Row cleared, button disabled
7. **New Row Addition:** Delete button initialized as disabled
8. **Template Row Cloning:** Delete button properly initialized

## Backward Compatibility

- Bulk delete functionality preserved but hidden
- Existing modal dialogs reused
- Original validation logic maintained
- Database schema unchanged (only added field retrieval)

## Performance Considerations

- AJAX calls only made when offender ID changes
- Efficient button state management
- Minimal DOM manipulation
- Error handling prevents unnecessary requests
